/**
 * MD-to-PDF Export API Route
 * Handles PDF export requests using the md-to-pdf library
 */

import { NextRequest, NextResponse } from 'next/server';
import { exportMarkdownToPdf, exportMarkdownToPdfWithHeaderFooter } from '@/services/mdToPdfService';
import type { PdfExportOptions } from '@/services/mdToPdfService';

interface ExportRequest {
  markdown: string;
  options?: PdfExportOptions & {
    title?: string;
    author?: string;
    date?: string;
    showPageNumbers?: boolean;
  };
}

export async function POST(request: NextRequest) {
  try {
    const body: ExportRequest = await request.json();
    
    if (!body.markdown || typeof body.markdown !== 'string') {
      return NextResponse.json(
        { error: 'Markdown content is required' },
        { status: 400 }
      );
    }

    const { markdown, options = {} } = body;

    // Determine if we need header/footer
    const needsHeaderFooter = options.showPageNumbers || options.title || options.author || options.date;

    let result;
    if (needsHeaderFooter) {
      result = await exportMarkdownToPdfWithHeaderFooter(markdown, options);
    } else {
      result = await exportMarkdownToPdf(markdown, options);
    }

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to generate PDF' },
        { status: 500 }
      );
    }

    if (!result.content) {
      return NextResponse.json(
        { error: 'No PDF content generated' },
        { status: 500 }
      );
    }

    // Return the PDF as a blob
    return new NextResponse(result.content, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${result.filename || 'document.pdf'}"`,
        'Content-Length': result.content.length.toString(),
      },
    });

  } catch (error) {
    console.error('PDF export API error:', error);
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? String(error) : undefined
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
