/**
 * Documentation Page - Complete guide for using the markdown editor
 */

import { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Documentation - How to Use the Markdown Editor',
  description: 'Complete guide to using our markdown editor. Learn about features, shortcuts, formatting, export options, and advanced tips.',
  keywords: ['markdown editor guide', 'markdown tutorial', 'markdown syntax', 'editor documentation', 'how to use markdown'],
};

export default function DocumentationPage() {
  const sections = [
    {
      title: "Getting Started",
      items: [
        "Opening the editor",
        "Creating your first document",
        "Understanding the interface",
        "Basic markdown syntax"
      ]
    },
    {
      title: "Editor Features",
      items: [
        "Real-time preview",
        "Syntax highlighting",
        "File management",
        "Auto-save functionality"
      ]
    },
    {
      title: "Formatting Guide",
      items: [
        "Headers and text formatting",
        "Lists and tables",
        "Links and images",
        "Code blocks and syntax highlighting"
      ]
    },
    {
      title: "Export Options",
      items: [
        "PDF export",
        "HTML export",
        "Markdown download",
        "Print functionality"
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link href="/" className="flex items-center">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">M</span>
              </div>
              <span className="ml-2 text-xl font-bold text-gray-900">MarkdownEditor</span>
            </Link>
            <Link 
              href="/editor"
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              Try Editor
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="lg:grid lg:grid-cols-4 lg:gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <nav className="sticky top-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Table of Contents</h3>
              <ul className="space-y-2">
                {sections.map((section, index) => (
                  <li key={index}>
                    <a 
                      href={`#${section.title.toLowerCase().replace(/\s+/g, '-')}`}
                      className="text-blue-600 hover:text-blue-800 font-medium"
                    >
                      {section.title}
                    </a>
                    <ul className="ml-4 mt-2 space-y-1">
                      {section.items.map((item, itemIndex) => (
                        <li key={itemIndex}>
                          <a 
                            href={`#${item.toLowerCase().replace(/\s+/g, '-')}`}
                            className="text-gray-600 hover:text-gray-800 text-sm"
                          >
                            {item}
                          </a>
                        </li>
                      ))}
                    </ul>
                  </li>
                ))}
              </ul>
            </nav>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 mt-8 lg:mt-0">
            <div className="bg-white rounded-lg shadow-sm p-8">
              <h1 className="text-4xl font-bold text-gray-900 mb-8">Documentation</h1>
              
              {/* Getting Started */}
              <section id="getting-started" className="mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-6">Getting Started</h2>
                
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">Opening the Editor</h3>
                    <p className="text-gray-700 mb-4">
                      Simply click the "Try Editor" button or navigate to the editor page. No signup or installation required.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">Creating Your First Document</h3>
                    <p className="text-gray-700 mb-4">
                      Start typing in the left panel to see your markdown rendered in real-time on the right panel.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">Understanding the Interface</h3>
                    <ul className="list-disc list-inside text-gray-700 space-y-2">
                      <li>Left panel: Markdown editor with syntax highlighting</li>
                      <li>Right panel: Live preview of rendered markdown</li>
                      <li>Top toolbar: File operations and export options</li>
                      <li>Sidebar: File management and navigation</li>
                    </ul>
                  </div>
                </div>
              </section>

              {/* Editor Features */}
              <section id="editor-features" className="mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-6">Editor Features</h2>
                
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">Real-time Preview</h3>
                    <p className="text-gray-700 mb-4">
                      See your markdown rendered instantly as you type. The preview updates automatically with synchronized scrolling.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">Syntax Highlighting</h3>
                    <p className="text-gray-700 mb-4">
                      Code blocks are automatically highlighted based on the specified language. Supports 100+ programming languages.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">File Management</h3>
                    <p className="text-gray-700 mb-4">
                      Create, rename, delete, and organize your markdown files. All files are saved locally in your browser.
                    </p>
                  </div>
                </div>
              </section>

              {/* Formatting Guide */}
              <section id="formatting-guide" className="mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-6">Formatting Guide</h2>
                
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">Headers and Text Formatting</h3>
                    <div className="bg-gray-100 p-4 rounded-lg font-mono text-sm">
                      <div># Header 1</div>
                      <div>## Header 2</div>
                      <div>**Bold text**</div>
                      <div>*Italic text*</div>
                      <div>`Inline code`</div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">Lists and Tables</h3>
                    <div className="bg-gray-100 p-4 rounded-lg font-mono text-sm">
                      <div>- Bullet point</div>
                      <div>1. Numbered list</div>
                      <div className="mt-2">| Column 1 | Column 2 |</div>
                      <div>|----------|----------|</div>
                      <div>| Cell 1   | Cell 2   |</div>
                    </div>
                  </div>
                </div>
              </section>

              {/* Export Options */}
              <section id="export-options" className="mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-6">Export Options</h2>
                
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">PDF Export</h3>
                    <p className="text-gray-700 mb-4">
                      Export your document as a high-quality PDF with professional formatting. Click the "Export PDF" button in the toolbar.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">Other Export Formats</h3>
                    <ul className="list-disc list-inside text-gray-700 space-y-2">
                      <li>HTML: Export as HTML file with embedded CSS</li>
                      <li>Markdown: Download the raw markdown file</li>
                      <li>Print: Use browser print functionality for physical copies</li>
                    </ul>
                  </div>
                </div>
              </section>

              {/* CTA */}
              <div className="bg-blue-50 p-6 rounded-lg text-center">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Ready to Start Writing?</h3>
                <Link
                  href="/editor"
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium inline-block"
                >
                  Open the Editor
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">M</span>
            </div>
            <span className="ml-2 text-xl font-bold">MarkdownEditor</span>
          </div>
          <p className="text-gray-400 text-sm">
            &copy; 2024 MarkdownEditor. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
