/**
 * Features Page - Detailed feature descriptions for SEO
 */

import { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Features - Professional Markdown Editor with Real-time Preview',
  description: 'Discover all features of our markdown editor: real-time preview, syntax highlighting, PDF export, file management, mobile support, and more.',
  keywords: ['markdown editor features', 'real-time preview', 'syntax highlighting', 'PDF export', 'file management'],
};

export default function FeaturesPage() {
  const features = [
    {
      title: "Real-time Preview",
      description: "See your markdown rendered instantly as you type with our live preview feature. No need to switch between tabs or refresh the page.",
      icon: "⚡",
      benefits: [
        "Instant feedback while writing",
        "Side-by-side editing and preview",
        "Synchronized scrolling",
        "Live table of contents generation"
      ]
    },
    {
      title: "Syntax Highlighting",
      description: "Beautiful code syntax highlighting for 100+ programming languages with multiple themes to choose from.",
      icon: "🎨",
      benefits: [
        "Support for 100+ languages",
        "Multiple color themes",
        "Automatic language detection",
        "Copy code with formatting"
      ]
    },
    {
      title: "PDF Export",
      description: "Export your markdown documents to high-quality PDFs with professional formatting and styling.",
      icon: "📄",
      benefits: [
        "One-click PDF generation",
        "Professional formatting",
        "Custom page layouts",
        "Optimized file sizes"
      ]
    },
    {
      title: "File Management",
      description: "Organize your documents with our built-in file management system. Create, rename, delete, and organize files easily.",
      icon: "📁",
      benefits: [
        "Create and organize files",
        "Drag and drop support",
        "File search functionality",
        "Auto-save capabilities"
      ]
    },
    {
      title: "Mobile Responsive",
      description: "Perfect editing experience on desktop, tablet, and mobile devices with touch-optimized interface.",
      icon: "📱",
      benefits: [
        "Touch-optimized interface",
        "Swipe navigation",
        "Mobile toolbar",
        "Responsive design"
      ]
    },
    {
      title: "Auto-Save",
      description: "Never lose your work with automatic saving to local storage and session backup features.",
      icon: "💾",
      benefits: [
        "Automatic content saving",
        "Session backup",
        "Recovery on browser crash",
        "Local storage persistence"
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link href="/" className="flex items-center">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">M</span>
              </div>
              <span className="ml-2 text-xl font-bold text-gray-900">MarkdownEditor</span>
            </Link>
            <Link 
              href="/editor"
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              Try Editor
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Powerful Features for Professional Markdown Editing
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Everything you need to write, edit, and export beautiful markdown documents. 
            From real-time preview to professional PDF export.
          </p>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">{feature.title}</h3>
                <p className="text-gray-600 mb-6">{feature.description}</p>
                <ul className="space-y-2">
                  {feature.benefits.map((benefit, benefitIndex) => (
                    <li key={benefitIndex} className="flex items-center text-sm text-gray-700">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      {benefit}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Experience These Features?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Start using our markdown editor today and boost your productivity
          </p>
          <Link
            href="/editor"
            className="bg-white text-blue-600 px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors font-semibold text-lg shadow-lg inline-block"
          >
            Try All Features Now
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">M</span>
            </div>
            <span className="ml-2 text-xl font-bold">MarkdownEditor</span>
          </div>
          <p className="text-gray-400 text-sm">
            &copy; 2024 MarkdownEditor. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
