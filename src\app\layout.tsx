import type { Metadata } from "next";
import "./globals.css";
import "./animations.css";
import "../styles/enhanced-markdown.css";
import "../styles/mobile-enhanced.css";
import { AppProvider } from "@/contexts/AppContext";
import { GoogleAnalytics } from "@/components/Analytics/GoogleAnalytics";
import { PerformanceMonitor } from "@/components/Analytics/PerformanceMonitor";

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover',
  themeColor: '#3b82f6'
};

export const metadata: Metadata = {
  title: "Best Online Markdown Editor - Free Real-time Preview & PDF Export",
  description: "Professional markdown editor with live preview, syntax highlighting, PDF export, and file management. Perfect for developers, writers, and students. No signup required.",
  keywords: [
    "markdown editor", "online markdown editor", "markdown preview", "markdown to pdf",
    "free markdown editor", "real-time markdown", "markdown converter", "markdown writing tool",
    "github markdown", "technical writing", "documentation tool", "markdown syntax highlighting"
  ],
  authors: [{ name: "MarkdownEditor Team" }],
  creator: "MarkdownEditor",
  publisher: "MarkdownEditor",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://markdowneditor.dev',
    siteName: 'MarkdownEditor',
    title: 'Best Online Markdown Editor - Free Real-time Preview & PDF Export',
    description: 'Professional markdown editor with live preview, syntax highlighting, PDF export, and file management. Perfect for developers, writers, and students.',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'MarkdownEditor - Professional Online Markdown Editor',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@markdowneditor',
    creator: '@markdowneditor',
    title: 'Best Online Markdown Editor - Free Real-time Preview & PDF Export',
    description: 'Professional markdown editor with live preview, syntax highlighting, PDF export, and file management. Perfect for developers, writers, and students.',
    images: ['/twitter-image.png'],
  },
  alternates: {
    canonical: 'https://markdowneditor.dev',
  },
  category: 'Technology',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="antialiased">
        <AppProvider>
          {children}
        </AppProvider>
        {/* Analytics and Performance Monitoring */}
        <GoogleAnalytics measurementId={process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || ''} />
        <PerformanceMonitor />
      </body>
    </html>
  );
}
