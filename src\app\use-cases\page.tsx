/**
 * Use Cases Page - Detailed use case descriptions for SEO
 */

import { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Use Cases - Markdown Editor for Developers, Writers & Students',
  description: 'Discover how developers, technical writers, students, and content creators use our markdown editor for documentation, blogging, note-taking, and more.',
  keywords: ['markdown for developers', 'technical documentation', 'markdown blogging', 'student note taking', 'content creation'],
};

export default function UseCasesPage() {
  const useCases = [
    {
      title: "Software Developers",
      description: "Create comprehensive documentation, README files, and technical guides",
      icon: "👨‍💻",
      scenarios: [
        {
          title: "README Files",
          description: "Write clear project documentation with code examples and installation instructions"
        },
        {
          title: "API Documentation",
          description: "Document REST APIs with request/response examples and parameter descriptions"
        },
        {
          title: "Technical Guides",
          description: "Create step-by-step tutorials and troubleshooting guides for your team"
        },
        {
          title: "Code Comments",
          description: "Draft detailed code comments and inline documentation before implementation"
        }
      ]
    },
    {
      title: "Technical Writers",
      description: "Produce professional documentation and user guides with ease",
      icon: "✍️",
      scenarios: [
        {
          title: "User Manuals",
          description: "Create comprehensive user guides with screenshots and step-by-step instructions"
        },
        {
          title: "Knowledge Base",
          description: "Build searchable knowledge bases with consistent formatting and structure"
        },
        {
          title: "Release Notes",
          description: "Document software updates, new features, and bug fixes clearly"
        },
        {
          title: "Process Documentation",
          description: "Document workflows, procedures, and best practices for teams"
        }
      ]
    },
    {
      title: "Students & Researchers",
      description: "Take notes, write papers, and organize research efficiently",
      icon: "🎓",
      scenarios: [
        {
          title: "Lecture Notes",
          description: "Take structured notes with headings, lists, and code snippets during lectures"
        },
        {
          title: "Research Papers",
          description: "Draft academic papers with proper citations and formatting"
        },
        {
          title: "Study Guides",
          description: "Create organized study materials with tables, diagrams, and summaries"
        },
        {
          title: "Lab Reports",
          description: "Document experiments and findings with embedded code and data tables"
        }
      ]
    },
    {
      title: "Content Creators & Bloggers",
      description: "Write engaging blog posts and articles with rich formatting",
      icon: "📝",
      scenarios: [
        {
          title: "Blog Posts",
          description: "Draft blog articles with proper headings, links, and embedded media"
        },
        {
          title: "Newsletter Content",
          description: "Create newsletter content with consistent formatting and structure"
        },
        {
          title: "Social Media Content",
          description: "Plan and draft social media posts with proper formatting"
        },
        {
          title: "Content Planning",
          description: "Organize content calendars and editorial workflows"
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link href="/" className="flex items-center">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">M</span>
              </div>
              <span className="ml-2 text-xl font-bold text-gray-900">MarkdownEditor</span>
            </Link>
            <Link 
              href="/editor"
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              Try Editor
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Markdown Editor for Every Professional
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From software development to academic research, discover how professionals 
            across industries use our markdown editor to boost their productivity.
          </p>
        </div>
      </section>

      {/* Use Cases */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-16">
            {useCases.map((useCase, index) => (
              <div key={index} className={`flex flex-col ${index % 2 === 1 ? 'lg:flex-row-reverse' : 'lg:flex-row'} items-center gap-12`}>
                <div className="flex-1">
                  <div className="text-6xl mb-6">{useCase.icon}</div>
                  <h2 className="text-3xl font-bold text-gray-900 mb-4">{useCase.title}</h2>
                  <p className="text-xl text-gray-600 mb-8">{useCase.description}</p>
                  <Link
                    href="/editor"
                    className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium inline-block"
                  >
                    Start Writing Now
                  </Link>
                </div>
                <div className="flex-1">
                  <div className="grid gap-6">
                    {useCase.scenarios.map((scenario, scenarioIndex) => (
                      <div key={scenarioIndex} className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">{scenario.title}</h3>
                        <p className="text-gray-600">{scenario.description}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Find Your Perfect Markdown Workflow
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Join thousands of professionals who have transformed their writing process
          </p>
          <Link
            href="/editor"
            className="bg-white text-blue-600 px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors font-semibold text-lg shadow-lg inline-block"
          >
            Get Started Today
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">M</span>
            </div>
            <span className="ml-2 text-xl font-bold">MarkdownEditor</span>
          </div>
          <p className="text-gray-400 text-sm">
            &copy; 2024 MarkdownEditor. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
