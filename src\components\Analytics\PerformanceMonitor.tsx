/**
 * Performance Monitoring Component
 * Tracks Core Web Vitals and user experience metrics
 */

'use client';

import { useEffect } from 'react';

interface PerformanceMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  timestamp: number;
}

export function PerformanceMonitor() {
  useEffect(() => {
    // Track Core Web Vitals
    const trackWebVitals = () => {
      // Largest Contentful Paint (LCP)
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'largest-contentful-paint') {
            const lcp = entry.startTime;
            const rating = lcp <= 2500 ? 'good' : lcp <= 4000 ? 'needs-improvement' : 'poor';
            
            reportMetric({
              name: 'LCP',
              value: lcp,
              rating,
              timestamp: Date.now()
            });
          }
        }
      });

      observer.observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay (FID)
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'first-input') {
            const fid = (entry as any).processingStart - entry.startTime;
            const rating = fid <= 100 ? 'good' : fid <= 300 ? 'needs-improvement' : 'poor';
            
            reportMetric({
              name: 'FID',
              value: fid,
              rating,
              timestamp: Date.now()
            });
          }
        }
      });

      fidObserver.observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift (CLS)
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'layout-shift' && !(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
      });

      clsObserver.observe({ entryTypes: ['layout-shift'] });

      // Report CLS when the page is about to be unloaded
      window.addEventListener('beforeunload', () => {
        const rating = clsValue <= 0.1 ? 'good' : clsValue <= 0.25 ? 'needs-improvement' : 'poor';
        reportMetric({
          name: 'CLS',
          value: clsValue,
          rating,
          timestamp: Date.now()
        });
      });

      // Time to First Byte (TTFB)
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigationEntry) {
        const ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
        const rating = ttfb <= 800 ? 'good' : ttfb <= 1800 ? 'needs-improvement' : 'poor';
        
        reportMetric({
          name: 'TTFB',
          value: ttfb,
          rating,
          timestamp: Date.now()
        });
      }
    };

    // Track custom performance metrics
    const trackCustomMetrics = () => {
      // Time to Interactive (TTI) approximation
      window.addEventListener('load', () => {
        setTimeout(() => {
          const tti = performance.now();
          const rating = tti <= 3800 ? 'good' : tti <= 7300 ? 'needs-improvement' : 'poor';
          
          reportMetric({
            name: 'TTI',
            value: tti,
            rating,
            timestamp: Date.now()
          });
        }, 0);
      });

      // Memory usage (if available)
      if ('memory' in performance) {
        const memoryInfo = (performance as any).memory;
        reportMetric({
          name: 'Memory_Used',
          value: memoryInfo.usedJSHeapSize,
          rating: 'good', // No standard rating for memory
          timestamp: Date.now()
        });
      }

      // Connection information
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        reportMetric({
          name: 'Connection_Type',
          value: connection.effectiveType === '4g' ? 4 : connection.effectiveType === '3g' ? 3 : 2,
          rating: 'good',
          timestamp: Date.now()
        });
      }
    };

    // Initialize tracking
    if (typeof window !== 'undefined') {
      trackWebVitals();
      trackCustomMetrics();
    }
  }, []);

  return null;
}

// Function to report metrics to analytics
const reportMetric = (metric: PerformanceMetric) => {
  // Send to Google Analytics if available
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'web_vitals', {
      event_category: 'performance',
      event_label: metric.name,
      value: Math.round(metric.value),
      custom_map: {
        metric_rating: metric.rating
      }
    });
  }

  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`Performance Metric - ${metric.name}:`, {
      value: metric.value,
      rating: metric.rating,
      timestamp: new Date(metric.timestamp).toISOString()
    });
  }

  // Store in localStorage for debugging
  try {
    const existingMetrics = JSON.parse(localStorage.getItem('performance_metrics') || '[]');
    existingMetrics.push(metric);
    
    // Keep only the last 50 metrics
    if (existingMetrics.length > 50) {
      existingMetrics.splice(0, existingMetrics.length - 50);
    }
    
    localStorage.setItem('performance_metrics', JSON.stringify(existingMetrics));
  } catch (error) {
    console.warn('Failed to store performance metric:', error);
  }
};

// Utility function to get performance metrics
export const getPerformanceMetrics = (): PerformanceMetric[] => {
  try {
    return JSON.parse(localStorage.getItem('performance_metrics') || '[]');
  } catch {
    return [];
  }
};

// Function to track user interactions
export const trackUserInteraction = (interaction: string, element?: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'user_interaction', {
      event_category: 'engagement',
      event_label: interaction,
      custom_map: {
        element: element
      }
    });
  }
};

// Function to track errors
export const trackError = (error: Error, context?: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'exception', {
      description: error.message,
      fatal: false,
      custom_map: {
        context: context,
        stack: error.stack
      }
    });
  }
};
