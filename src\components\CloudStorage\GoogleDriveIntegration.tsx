/**
 * Google Drive Integration Component
 * Allows users to save files to their Google Drive account
 */

'use client';

import React, { useState, useEffect } from 'react';

interface GoogleDriveIntegrationProps {
  content: string;
  filename: string;
  onSaveComplete?: (fileId: string) => void;
  onSaveError?: (error: string) => void;
}

declare global {
  interface Window {
    gapi: any;
    google: any;
  }
}

export const GoogleDriveIntegration: React.FC<GoogleDriveIntegrationProps> = ({
  content,
  filename,
  onSaveComplete,
  onSaveError,
}) => {
  const [isSignedIn, setIsSignedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showDialog, setShowDialog] = useState(false);

  // Google API configuration
  const CLIENT_ID = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '';
  const API_KEY = process.env.NEXT_PUBLIC_GOOGLE_API_KEY || '';
  const DISCOVERY_DOC = 'https://www.googleapis.com/discovery/v1/apis/drive/v3/rest';
  const SCOPES = 'https://www.googleapis.com/auth/drive.file';

  useEffect(() => {
    initializeGapi();
  }, []);

  const initializeGapi = async () => {
    if (!CLIENT_ID || !API_KEY) {
      console.warn('Google Drive integration not configured. Please set GOOGLE_CLIENT_ID and GOOGLE_API_KEY.');
      return;
    }

    try {
      setIsLoading(true);
      
      // Load Google API
      await loadGoogleAPI();
      
      // Initialize gapi
      await window.gapi.load('auth2', initAuth);
      await window.gapi.load('client', initClient);
      
    } catch (error) {
      console.error('Failed to initialize Google API:', error);
      onSaveError?.('Failed to initialize Google Drive integration');
    } finally {
      setIsLoading(false);
    }
  };

  const loadGoogleAPI = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (window.gapi) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://apis.google.com/js/api.js';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Google API'));
      document.head.appendChild(script);
    });
  };

  const initAuth = async () => {
    const authInstance = window.gapi.auth2.getAuthInstance();
    if (authInstance) {
      setIsSignedIn(authInstance.isSignedIn.get());
      
      // Listen for sign-in state changes
      authInstance.isSignedIn.listen(setIsSignedIn);
    }
  };

  const initClient = async () => {
    await window.gapi.client.init({
      apiKey: API_KEY,
      clientId: CLIENT_ID,
      discoveryDocs: [DISCOVERY_DOC],
      scope: SCOPES
    });

    const authInstance = window.gapi.auth2.getAuthInstance();
    setIsSignedIn(authInstance.isSignedIn.get());
  };

  const signIn = async () => {
    try {
      const authInstance = window.gapi.auth2.getAuthInstance();
      await authInstance.signIn();
    } catch (error) {
      console.error('Sign-in failed:', error);
      onSaveError?.('Failed to sign in to Google Drive');
    }
  };

  const signOut = async () => {
    try {
      const authInstance = window.gapi.auth2.getAuthInstance();
      await authInstance.signOut();
    } catch (error) {
      console.error('Sign-out failed:', error);
    }
  };

  const saveToGoogleDrive = async () => {
    if (!isSignedIn) {
      await signIn();
      return;
    }

    try {
      setIsSaving(true);

      const fileMetadata = {
        name: filename.endsWith('.md') ? filename : `${filename}.md`,
        parents: ['root'] // Save to root folder
      };

      const form = new FormData();
      form.append('metadata', new Blob([JSON.stringify(fileMetadata)], { type: 'application/json' }));
      form.append('file', new Blob([content], { type: 'text/markdown' }));

      const response = await fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${window.gapi.auth2.getAuthInstance().currentUser.get().getAuthResponse().access_token}`
        },
        body: form
      });

      if (!response.ok) {
        throw new Error('Failed to save file to Google Drive');
      }

      const result = await response.json();
      onSaveComplete?.(result.id);
      setShowDialog(false);
      
    } catch (error) {
      console.error('Save to Google Drive failed:', error);
      onSaveError?.(error instanceof Error ? error.message : 'Failed to save to Google Drive');
    } finally {
      setIsSaving(false);
    }
  };

  if (!CLIENT_ID || !API_KEY) {
    return null; // Don't render if not configured
  }

  return (
    <>
      <button
        onClick={() => setShowDialog(true)}
        disabled={isLoading}
        className="flex items-center gap-2 px-4 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50"
        title="Save to Google Drive"
      >
        <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
          <path d="M6.5 12L9 7h6l2.5 5H6.5zm11.5 1L15 18H9l-3-5h12zm-6.5-8L8 10H4l4-7h3.5zm1 0H16l4 7h-4L12 5z" />
        </svg>
        {isLoading ? 'Loading...' : 'Save to Drive'}
      </button>

      {/* Save Dialog */}
      {showDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <svg className="w-8 h-8 mr-3" viewBox="0 0 24 24" fill="currentColor">
                <path d="M6.5 12L9 7h6l2.5 5H6.5zm11.5 1L15 18H9l-3-5h12zm-6.5-8L8 10H4l4-7h3.5zm1 0H16l4 7h-4L12 5z" />
              </svg>
              <h3 className="text-lg font-semibold text-gray-900">Save to Google Drive</h3>
            </div>

            <p className="text-gray-600 mb-4">
              Save your markdown file "{filename}" to your Google Drive account.
            </p>

            {!isSignedIn ? (
              <div className="mb-4 p-3 bg-blue-50 rounded-md">
                <p className="text-sm text-blue-800">
                  You need to sign in to your Google account to save files to Google Drive.
                </p>
              </div>
            ) : (
              <div className="mb-4 p-3 bg-green-50 rounded-md">
                <p className="text-sm text-green-800">
                  ✓ Signed in to Google Drive. Ready to save your file.
                </p>
              </div>
            )}

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDialog(false)}
                disabled={isSaving}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              
              {isSignedIn ? (
                <button
                  onClick={saveToGoogleDrive}
                  disabled={isSaving}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center"
                >
                  {isSaving ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    'Save to Drive'
                  )}
                </button>
              ) : (
                <button
                  onClick={signIn}
                  disabled={isLoading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
                >
                  Sign in to Google
                </button>
              )}
            </div>

            {isSignedIn && (
              <div className="mt-3 text-center">
                <button
                  onClick={signOut}
                  className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
                >
                  Sign out
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};
