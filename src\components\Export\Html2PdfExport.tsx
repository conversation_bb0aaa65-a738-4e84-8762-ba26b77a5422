/**
 * Simple HTML2PDF Export Component
 * Client-side PDF export using html2pdf.js with ReactMarkdown
 * This is the preferred simple approach as per user requirements
 */

'use client';

import React, { useState, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { trackPdfExport, trackFeatureUsage } from '@/components/Analytics/GoogleAnalytics';

interface Html2PdfExportProps {
  markdown: string;
  filename?: string;
  onExportStart?: () => void;
  onExportComplete?: (filename: string) => void;
  onExportError?: (error: string) => void;
}

export const Html2PdfExport: React.FC<Html2PdfExportProps> = ({
  markdown,
  filename = 'document.pdf',
  onExportStart,
  onExportComplete,
  onExportError,
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  const exportToPdf = async () => {
    if (isExporting || !markdown.trim()) return;

    try {
      setIsExporting(true);
      onExportStart?.();

      // Always show preview temporarily to ensure content is rendered
      setShowPreview(true);
      // Wait for content to render
      await new Promise(resolve => setTimeout(resolve, 200));

      // Dynamically import html2pdf.js to avoid SSR issues
      const html2pdf = (await import('html2pdf.js')).default;

      if (!contentRef.current) {
        throw new Error('Content reference not found. Please try again.');
      }

      // Configure html2pdf options - simplified to avoid oklch color issues
      const options = {
        margin: 0.5,
        filename: filename.endsWith('.pdf') ? filename : `${filename}.pdf`,
        image: {
          type: 'jpeg',
          quality: 0.9
        },
        html2canvas: {
          scale: 1.5, // Reduced scale for better performance
          useCORS: true,
          letterRendering: true,
          allowTaint: true, // Allow taint to handle modern CSS
          backgroundColor: '#ffffff',
          logging: false,
          ignoreElements: (element) => {
            // Ignore elements that might cause oklch issues
            const computedStyle = window.getComputedStyle(element);
            return computedStyle.color?.includes('oklch') ||
                   computedStyle.backgroundColor?.includes('oklch') ||
                   element.classList.contains('ignore-pdf');
          }
        },
        jsPDF: {
          unit: 'in',
          format: 'a4',
          orientation: 'portrait',
          compress: true
        }
      };

      console.log('Starting PDF generation...');

      // Generate and download PDF
      await html2pdf().set(options).from(contentRef.current).save();

      console.log('PDF generated successfully');

      // Track PDF export
      trackPdfExport();
      trackFeatureUsage('pdf_export');

      // Hide preview after export
      setShowPreview(false);

      onExportComplete?.(options.filename);
    } catch (error) {
      console.error('PDF export failed:', error);

      let errorMessage = 'Export failed';
      if (error instanceof Error) {
        errorMessage = error.message;

        // Provide helpful error messages
        if (error.message.includes('html2pdf')) {
          errorMessage = 'PDF library not available. Please refresh the page and try again.';
        } else if (error.message.includes('Content reference')) {
          errorMessage = 'Content not ready. Please wait a moment and try again.';
        }
      }

      onExportError?.(errorMessage);
    } finally {
      setIsExporting(false);
    }
  };



  return (
    <div className="html2pdf-export">
      {/* Export Button */}
      <button
        onClick={exportToPdf}
        disabled={isExporting || !markdown.trim()}
        className={`px-4 py-2 rounded-md transition-colors duration-200 flex items-center gap-2 ${
          isExporting || !markdown.trim()
            ? 'bg-gray-400 text-white cursor-not-allowed'
            : 'bg-blue-600 text-white hover:bg-blue-700'
        }`}
        title="Export as PDF"
      >
        {isExporting ? (
          <>
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            Exporting...
          </>
        ) : (
          <>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export PDF
          </>
        )}
      </button>

      {/* Hidden content for PDF generation */}
      <div
        ref={contentRef}
        className={`${showPreview ? 'block' : 'hidden'} mt-4 p-6 bg-white border border-gray-200 rounded-lg shadow-sm ignore-pdf`}
        style={{
          fontFamily: 'Arial, sans-serif', // Use simple font to avoid issues
          lineHeight: '1.6',
          color: '#000000', // Use hex colors instead of modern CSS
          backgroundColor: '#ffffff',
          maxWidth: '210mm', // A4 width
          margin: '0 auto',
          // Override any potential oklch colors
          '--tw-text-opacity': '1',
          '--tw-bg-opacity': '1'
        }}
      >
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          components={{
            code({ node, inline, className, children, ...props }) {
              const match = /language-(\w+)/.exec(className || '');
              return !inline && match ? (
                <SyntaxHighlighter
                  style={oneLight}
                  language={match[1]}
                  PreTag="div"
                  {...props}
                >
                  {String(children).replace(/\n$/, '')}
                </SyntaxHighlighter>
              ) : (
                <code className={className} {...props}>
                  {children}
                </code>
              );
            },
            // Simplified styling to avoid CSS parsing issues
            h1: ({ children }) => (
              <h1 style={{
                fontSize: '32px',
                fontWeight: 'bold',
                marginTop: '24px',
                marginBottom: '16px',
                borderBottom: '1px solid #cccccc',
                paddingBottom: '8px',
                color: '#000000'
              }}>
                {children}
              </h1>
            ),
            h2: ({ children }) => (
              <h2 style={{
                fontSize: '24px',
                fontWeight: 'bold',
                marginTop: '24px',
                marginBottom: '16px',
                borderBottom: '1px solid #cccccc',
                paddingBottom: '8px',
                color: '#000000'
              }}>
                {children}
              </h2>
            ),
            h3: ({ children }) => (
              <h3 style={{
                fontSize: '20px',
                fontWeight: 'bold',
                marginTop: '24px',
                marginBottom: '16px',
                color: '#000000'
              }}>
                {children}
              </h3>
            ),
            p: ({ children }) => (
              <p style={{
                marginBottom: '16px',
                color: '#000000',
                lineHeight: '1.6'
              }}>
                {children}
              </p>
            ),
            blockquote: ({ children }) => (
              <blockquote style={{
                padding: '0 16px',
                color: '#666666',
                borderLeft: '4px solid #cccccc',
                margin: '0 0 16px 0',
                fontStyle: 'italic'
              }}>
                {children}
              </blockquote>
            ),
            table: ({ children }) => (
              <table style={{
                borderCollapse: 'collapse',
                marginBottom: '16px',
                width: '100%',
                border: '1px solid #cccccc'
              }}>
                {children}
              </table>
            ),
            th: ({ children }) => (
              <th style={{
                padding: '8px 12px',
                border: '1px solid #cccccc',
                fontWeight: 'bold',
                backgroundColor: '#f5f5f5',
                color: '#000000'
              }}>
                {children}
              </th>
            ),
            td: ({ children }) => (
              <td style={{
                padding: '8px 12px',
                border: '1px solid #cccccc',
                color: '#000000'
              }}>
                {children}
              </td>
            ),
          }}
        >
          {markdown}
        </ReactMarkdown>
      </div>


    </div>
  );
};
