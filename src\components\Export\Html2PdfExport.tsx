/**
 * Simple HTML2PDF Export Component
 * Client-side PDF export using html2pdf.js with ReactMarkdown
 * This is the preferred simple approach as per user requirements
 */

'use client';

import React, { useState, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { trackPdfExport, trackFeatureUsage } from '@/components/Analytics/GoogleAnalytics';

interface Html2PdfExportProps {
  markdown: string;
  filename?: string;
  onExportStart?: () => void;
  onExportComplete?: (filename: string) => void;
  onExportError?: (error: string) => void;
}

export const Html2PdfExport: React.FC<Html2PdfExportProps> = ({
  markdown,
  filename = 'document.pdf',
  onExportStart,
  onExportComplete,
  onExportError,
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  const exportToPdf = async () => {
    if (isExporting || !markdown.trim()) return;

    try {
      setIsExporting(true);
      onExportStart?.();

      // Show preview temporarily to ensure content is rendered
      const wasShowingPreview = showPreview;
      if (!showPreview) {
        setShowPreview(true);
        // Wait a bit for the content to render
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Dynamically import html2pdf.js to avoid SSR issues
      const html2pdf = (await import('html2pdf.js')).default;

      if (!contentRef.current) {
        throw new Error('Content reference not found. Please try again.');
      }

      // Configure html2pdf options for better quality
      const options = {
        margin: 0.5,
        filename: filename.endsWith('.pdf') ? filename : `${filename}.pdf`,
        image: {
          type: 'jpeg',
          quality: 0.95
        },
        html2canvas: {
          scale: 2,
          useCORS: true,
          letterRendering: true,
          allowTaint: false,
          backgroundColor: '#ffffff',
          logging: false,
          width: 794, // A4 width in pixels at 96 DPI
          height: 1123 // A4 height in pixels at 96 DPI
        },
        jsPDF: {
          unit: 'in',
          format: 'a4',
          orientation: 'portrait',
          compress: true
        }
      };

      console.log('Starting PDF generation...');

      // Generate and download PDF
      await html2pdf().set(options).from(contentRef.current).save();

      console.log('PDF generated successfully');

      // Track PDF export
      trackPdfExport();
      trackFeatureUsage('pdf_export');

      // Restore preview state
      if (!wasShowingPreview) {
        setShowPreview(false);
      }

      onExportComplete?.(options.filename);
    } catch (error) {
      console.error('PDF export failed:', error);

      let errorMessage = 'Export failed';
      if (error instanceof Error) {
        errorMessage = error.message;

        // Provide helpful error messages
        if (error.message.includes('html2pdf')) {
          errorMessage = 'PDF library not available. Please refresh the page and try again.';
        } else if (error.message.includes('Content reference')) {
          errorMessage = 'Content not ready. Please wait a moment and try again.';
        }
      }

      onExportError?.(errorMessage);
    } finally {
      setIsExporting(false);
    }
  };

  const togglePreview = () => {
    setShowPreview(!showPreview);
  };

  return (
    <div className="html2pdf-export">
      {/* Export Button */}
      <div className="flex items-center gap-2">
        <button
          onClick={exportToPdf}
          disabled={isExporting || !markdown.trim()}
          className={`px-4 py-2 rounded-md transition-colors duration-200 flex items-center gap-2 ${
            isExporting || !markdown.trim()
              ? 'bg-gray-400 text-white cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
          title="Export as PDF using html2pdf.js"
        >
          {isExporting ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Exporting...
            </>
          ) : (
            <>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Export PDF
            </>
          )}
        </button>

        {/* Preview Toggle */}
        <button
          onClick={togglePreview}
          disabled={isExporting}
          className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
          title="Toggle PDF Preview"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
        </button>
      </div>

      {/* Hidden content for PDF generation */}
      <div 
        ref={contentRef}
        className={`${showPreview ? 'block' : 'hidden'} mt-4 p-6 bg-white border border-gray-200 rounded-lg shadow-sm`}
        style={{
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          lineHeight: '1.6',
          color: '#24292e',
          maxWidth: '210mm', // A4 width
          margin: '0 auto'
        }}
      >
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          components={{
            code({ node, inline, className, children, ...props }) {
              const match = /language-(\w+)/.exec(className || '');
              return !inline && match ? (
                <SyntaxHighlighter
                  style={oneLight}
                  language={match[1]}
                  PreTag="div"
                  {...props}
                >
                  {String(children).replace(/\n$/, '')}
                </SyntaxHighlighter>
              ) : (
                <code className={className} {...props}>
                  {children}
                </code>
              );
            },
            // Custom styling for better PDF output
            h1: ({ children }) => (
              <h1 style={{ 
                fontSize: '2em', 
                fontWeight: '600', 
                marginTop: '24px', 
                marginBottom: '16px',
                borderBottom: '1px solid #eaecef',
                paddingBottom: '0.3em'
              }}>
                {children}
              </h1>
            ),
            h2: ({ children }) => (
              <h2 style={{ 
                fontSize: '1.5em', 
                fontWeight: '600', 
                marginTop: '24px', 
                marginBottom: '16px',
                borderBottom: '1px solid #eaecef',
                paddingBottom: '0.3em'
              }}>
                {children}
              </h2>
            ),
            h3: ({ children }) => (
              <h3 style={{ 
                fontSize: '1.25em', 
                fontWeight: '600', 
                marginTop: '24px', 
                marginBottom: '16px'
              }}>
                {children}
              </h3>
            ),
            p: ({ children }) => (
              <p style={{ marginBottom: '16px' }}>
                {children}
              </p>
            ),
            blockquote: ({ children }) => (
              <blockquote style={{
                padding: '0 1em',
                color: '#6a737d',
                borderLeft: '0.25em solid #dfe2e5',
                margin: '0 0 16px 0'
              }}>
                {children}
              </blockquote>
            ),
            table: ({ children }) => (
              <table style={{
                borderCollapse: 'collapse',
                marginBottom: '16px',
                width: '100%'
              }}>
                {children}
              </table>
            ),
            th: ({ children }) => (
              <th style={{
                padding: '6px 13px',
                border: '1px solid #dfe2e5',
                fontWeight: '600',
                backgroundColor: '#f6f8fa'
              }}>
                {children}
              </th>
            ),
            td: ({ children }) => (
              <td style={{
                padding: '6px 13px',
                border: '1px solid #dfe2e5'
              }}>
                {children}
              </td>
            ),
          }}
        >
          {markdown}
        </ReactMarkdown>
      </div>

      {showPreview && (
        <div className="mt-2 text-sm text-gray-600">
          <p>👆 This is how your PDF will look. Click "Export PDF" to download.</p>
        </div>
      )}
    </div>
  );
};
