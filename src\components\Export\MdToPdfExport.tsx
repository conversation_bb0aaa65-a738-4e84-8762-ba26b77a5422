/**
 * MD-to-PDF Export Component
 * Simple, clean PDF export using the md-to-pdf library
 */

'use client';

import React, { useState } from 'react';
import type { PdfExportOptions } from '@/services/mdToPdfService';

interface MdToPdfExportProps {
  markdown: string;
  filename?: string;
  onExportStart?: () => void;
  onExportComplete?: (filename: string) => void;
  onExportError?: (error: string) => void;
}

export const MdToPdfExport: React.FC<MdToPdfExportProps> = ({
  markdown,
  filename = 'document.pdf',
  onExportStart,
  onExportComplete,
  onExportError,
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  const [options, setOptions] = useState<PdfExportOptions>({
    format: 'A4',
    margin: '20mm',
    printBackground: true,
    landscape: false,
    scale: 1,
  });

  const exportToPdf = async (customOptions?: Partial<PdfExportOptions>) => {
    if (isExporting || !markdown.trim()) return;

    try {
      setIsExporting(true);
      onExportStart?.();

      const exportOptions = { ...options, ...customOptions, filename };

      // Call the API endpoint for PDF export
      const response = await fetch('/api/export/md-to-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          markdown,
          options: exportOptions,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to export PDF');
      }

      // Get the PDF blob
      const blob = await response.blob();
      
      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename.endsWith('.pdf') ? filename : `${filename}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      onExportComplete?.(link.download);
    } catch (error) {
      console.error('PDF export failed:', error);
      onExportError?.(error instanceof Error ? error.message : 'Export failed');
    } finally {
      setIsExporting(false);
    }
  };

  const quickExport = () => exportToPdf();

  const exportWithHeaderFooter = () => {
    const today = new Date().toLocaleDateString();
    exportToPdf({
      showPageNumbers: true,
      title: filename.replace(/\.pdf$/i, ''),
      date: today,
      displayHeaderFooter: true,
      margin: '30mm 20mm',
    });
  };

  return (
    <div className="md-to-pdf-export">
      {/* Main Export Button */}
      <div className="flex items-center gap-2">
        <button
          onClick={quickExport}
          disabled={isExporting || !markdown.trim()}
          className={`px-4 py-2 rounded-md transition-colors duration-200 flex items-center gap-2 ${
            isExporting || !markdown.trim()
              ? 'bg-gray-400 text-white cursor-not-allowed'
              : 'bg-red-600 text-white hover:bg-red-700'
          }`}
          title="Export as PDF using md-to-pdf"
        >
          {isExporting ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Exporting...
            </>
          ) : (
            <>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Export PDF
            </>
          )}
        </button>

        {/* Options Toggle */}
        <button
          onClick={() => setShowOptions(!showOptions)}
          disabled={isExporting}
          className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
          title="PDF Export Options"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
          </svg>
        </button>
      </div>

      {/* Options Panel */}
      {showOptions && (
        <div className="mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
          <h3 className="text-sm font-medium text-gray-900 mb-3">PDF Export Options</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Format */}
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Page Format
              </label>
              <select
                value={options.format}
                onChange={(e) => setOptions(prev => ({ ...prev, format: e.target.value as any }))}
                className="w-full px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="A4">A4</option>
                <option value="A3">A3</option>
                <option value="A5">A5</option>
                <option value="Letter">Letter</option>
                <option value="Legal">Legal</option>
              </select>
            </div>

            {/* Margin */}
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Margin
              </label>
              <select
                value={options.margin}
                onChange={(e) => setOptions(prev => ({ ...prev, margin: e.target.value }))}
                className="w-full px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="10mm">Small (10mm)</option>
                <option value="20mm">Medium (20mm)</option>
                <option value="30mm">Large (30mm)</option>
                <option value="1in">1 inch</option>
              </select>
            </div>

            {/* Landscape */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="landscape"
                checked={options.landscape}
                onChange={(e) => setOptions(prev => ({ ...prev, landscape: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="landscape" className="ml-2 text-xs font-medium text-gray-700">
                Landscape orientation
              </label>
            </div>

            {/* Print Background */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="printBackground"
                checked={options.printBackground}
                onChange={(e) => setOptions(prev => ({ ...prev, printBackground: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="printBackground" className="ml-2 text-xs font-medium text-gray-700">
                Print background colors
              </label>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-4 flex gap-2">
            <button
              onClick={quickExport}
              disabled={isExporting}
              className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 transition-colors duration-200"
            >
              Export with Options
            </button>
            
            <button
              onClick={exportWithHeaderFooter}
              disabled={isExporting}
              className="px-3 py-1 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 transition-colors duration-200"
            >
              Export with Header/Footer
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
