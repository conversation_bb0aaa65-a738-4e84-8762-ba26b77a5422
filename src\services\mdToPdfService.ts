/**
 * MD-to-PDF Service
 * Clean, simple PDF export using the md-to-pdf library
 */

import { mdToPdf } from 'md-to-pdf';
import type { Config } from 'md-to-pdf';

export interface PdfExportOptions {
  filename?: string;
  format?: 'A4' | 'A3' | 'A5' | 'Letter' | 'Legal';
  margin?: string;
  printBackground?: boolean;
  headerTemplate?: string;
  footerTemplate?: string;
  displayHeaderFooter?: boolean;
  landscape?: boolean;
  scale?: number;
  preferCSSPageSize?: boolean;
}

export interface PdfExportResult {
  success: boolean;
  filename?: string;
  content?: Buffer;
  error?: string;
}

/**
 * Default configuration for PDF generation
 */
const getDefaultConfig = (options: PdfExportOptions = {}): Config => ({
  // PDF options
  pdf_options: {
    format: options.format || 'A4',
    margin: options.margin || '20mm',
    printBackground: options.printBackground !== false,
    displayHeaderFooter: options.displayHeaderFooter || false,
    headerTemplate: options.headerTemplate || '',
    footerTemplate: options.footerTemplate || '',
    landscape: options.landscape || false,
    scale: options.scale || 1,
    preferCSSPageSize: options.preferCSSPageSize || false,
    // Add timeout and other browser options
    timeout: 30000,
    waitForSelector: '.markdown-body',
  },

  // Styling options - use inline CSS to avoid external dependency issues
  stylesheet: `
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
      color: #24292e;
      background-color: #fff;
      max-width: 100%;
      margin: 0;
      padding: 20px;
    }
    .markdown-body {
      box-sizing: border-box;
      min-width: 200px;
      max-width: 100%;
      margin: 0 auto;
    }
    h1, h2, h3, h4, h5, h6 {
      margin-top: 24px;
      margin-bottom: 16px;
      font-weight: 600;
      line-height: 1.25;
    }
    h1 { font-size: 2em; border-bottom: 1px solid #eaecef; padding-bottom: 0.3em; }
    h2 { font-size: 1.5em; border-bottom: 1px solid #eaecef; padding-bottom: 0.3em; }
    h3 { font-size: 1.25em; }
    h4 { font-size: 1em; }
    h5 { font-size: 0.875em; }
    h6 { font-size: 0.85em; color: #6a737d; }
    p { margin-bottom: 16px; }
    blockquote {
      padding: 0 1em;
      color: #6a737d;
      border-left: 0.25em solid #dfe2e5;
      margin: 0 0 16px 0;
    }
    ul, ol { padding-left: 2em; margin-bottom: 16px; }
    li { margin-bottom: 0.25em; }
    code {
      padding: 0.2em 0.4em;
      margin: 0;
      font-size: 85%;
      background-color: rgba(27,31,35,0.05);
      border-radius: 3px;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    }
    pre {
      padding: 16px;
      overflow: auto;
      font-size: 85%;
      line-height: 1.45;
      background-color: #f6f8fa;
      border-radius: 6px;
      margin-bottom: 16px;
    }
    pre code {
      background-color: transparent;
      border: 0;
      display: inline;
      line-height: inherit;
      margin: 0;
      max-width: auto;
      overflow: visible;
      padding: 0;
      word-wrap: normal;
    }
    table {
      border-spacing: 0;
      border-collapse: collapse;
      margin-bottom: 16px;
      width: 100%;
    }
    table th, table td {
      padding: 6px 13px;
      border: 1px solid #dfe2e5;
    }
    table th {
      font-weight: 600;
      background-color: #f6f8fa;
    }
    table tr:nth-child(2n) {
      background-color: #f6f8fa;
    }
    img {
      max-width: 100%;
      height: auto;
    }
    hr {
      height: 0.25em;
      padding: 0;
      margin: 24px 0;
      background-color: #e1e4e8;
      border: 0;
    }
    strong { font-weight: 600; }
    em { font-style: italic; }
    del { text-decoration: line-through; }
  `,
  
  body_class: ['markdown-body'],
  
  // Custom CSS for better PDF output
  css: `
    .markdown-body {
      box-sizing: border-box;
      min-width: 200px;
      max-width: 980px;
      margin: 0 auto;
      padding: 45px;
      font-size: 12px;
      line-height: 1.6;
    }
    
    .page-break {
      page-break-after: always;
    }
    
    .markdown-body pre > code {
      white-space: pre-wrap;
      word-break: break-word;
    }
    
    .markdown-body table {
      width: 100%;
      overflow: auto;
      break-inside: auto;
    }
    
    .markdown-body table tr {
      break-inside: avoid;
      break-after: auto;
    }
    
    .markdown-body table thead {
      display: table-header-group;
    }
    
    .markdown-body table tfoot {
      display: table-footer-group;
    }
    
    .markdown-body img {
      max-width: 100%;
      height: auto;
    }
    
    .markdown-body blockquote {
      border-left: 4px solid #dfe2e5;
      padding: 0 16px;
      color: #6a737d;
    }
    
    .markdown-body h1, .markdown-body h2 {
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }
    
    @media print {
      .markdown-body {
        font-size: 11px;
      }
      
      .markdown-body h1 {
        break-after: avoid;
      }
      
      .markdown-body h2, .markdown-body h3, .markdown-body h4, .markdown-body h5, .markdown-body h6 {
        break-after: avoid;
        break-inside: avoid;
      }
      
      .markdown-body pre {
        break-inside: avoid;
      }
    }
  `,
  
  // Syntax highlighting
  highlight_style: 'github',
  
  // Marked options for better markdown parsing
  marked_options: {
    gfm: true,
    breaks: true,
    headerIds: true,
    mangle: false,
  },
  
  // Launch options for Puppeteer
  launch_options: {
    headless: true,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu'
    ]
  }
});

/**
 * Export markdown content to PDF using md-to-pdf
 */
export async function exportMarkdownToPdf(
  markdown: string,
  options: PdfExportOptions = {}
): Promise<PdfExportResult> {
  try {
    console.log('Starting PDF export with options:', options);

    const config = getDefaultConfig(options);
    console.log('Generated config:', JSON.stringify(config, null, 2));

    // Wrap markdown content in a proper HTML structure
    const wrappedMarkdown = `<div class="markdown-body">\n\n${markdown}\n\n</div>`;

    // Generate PDF from markdown content
    const pdf = await mdToPdf(
      { content: wrappedMarkdown },
      config
    );

    if (!pdf || !pdf.content) {
      throw new Error('Failed to generate PDF content - no content returned');
    }

    console.log('PDF generated successfully, size:', pdf.content.length, 'bytes');

    return {
      success: true,
      filename: options.filename || 'document.pdf',
      content: pdf.content
    };

  } catch (error) {
    console.error('PDF export error:', error);

    // Provide more detailed error information
    let errorMessage = 'Unknown error occurred';
    if (error instanceof Error) {
      errorMessage = error.message;

      // Check for common issues and provide helpful messages
      if (error.message.includes('ENOENT')) {
        errorMessage = 'Required files not found. Please ensure all dependencies are properly installed.';
      } else if (error.message.includes('timeout')) {
        errorMessage = 'PDF generation timed out. Please try again with a smaller document.';
      } else if (error.message.includes('browser')) {
        errorMessage = 'Browser engine not available. Please ensure Puppeteer or Playwright is properly installed.';
      }
    }

    return {
      success: false,
      error: errorMessage
    };
  }
}

/**
 * Export markdown file to PDF using md-to-pdf
 */
export async function exportMarkdownFileToPdf(
  filePath: string,
  options: PdfExportOptions = {}
): Promise<PdfExportResult> {
  try {
    const config = getDefaultConfig(options);
    
    // Set destination if filename is provided
    if (options.filename) {
      config.dest = options.filename;
    }
    
    // Generate PDF from markdown file
    const pdf = await mdToPdf(
      { path: filePath },
      config
    );

    if (!pdf || !pdf.content) {
      throw new Error('Failed to generate PDF content');
    }

    return {
      success: true,
      filename: pdf.filename || options.filename || 'document.pdf',
      content: pdf.content
    };

  } catch (error) {
    console.error('PDF export error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Create a PDF with custom header and footer
 */
export async function exportMarkdownToPdfWithHeaderFooter(
  markdown: string,
  options: PdfExportOptions & {
    title?: string;
    author?: string;
    date?: string;
    showPageNumbers?: boolean;
  } = {}
): Promise<PdfExportResult> {
  const headerTemplate = options.headerTemplate || `
    <style>
      .header {
        margin: 0 auto;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 10px;
        color: #666;
        text-align: center;
        width: 100%;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
      }
    </style>
    <div class="header">
      <span>${options.title || 'Document'}</span>
      ${options.date ? `<span style="float: right;">${options.date}</span>` : ''}
    </div>
  `;

  const footerTemplate = options.footerTemplate || (options.showPageNumbers ? `
    <style>
      .footer {
        margin: 0 auto;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 10px;
        color: #666;
        text-align: center;
        width: 100%;
        padding: 10px 0;
        border-top: 1px solid #eee;
      }
    </style>
    <div class="footer">
      <span>Page <span class="pageNumber"></span> of <span class="totalPages"></span></span>
      ${options.author ? `<span style="float: left;">${options.author}</span>` : ''}
    </div>
  ` : '');

  return exportMarkdownToPdf(markdown, {
    ...options,
    displayHeaderFooter: !!(headerTemplate || footerTemplate),
    headerTemplate,
    footerTemplate,
    margin: options.margin || '30mm 20mm'
  });
}
